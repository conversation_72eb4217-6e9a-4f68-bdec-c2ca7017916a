const REPORTING_ID = "1trjbOiLgOzrzv57AAomOfXjFQOmWsRS3cT_tvdRzdhg";
const OPS_REPORTING = "1MHbY6Oz-SiFloV5re7FSHDNqDf4WTMbKtGN1oqbrPcg"; 
const DE_CONTR_INFO = "1V7LCdx7YrNr0_GRyfgSZTEoDsBtgcnF3i3RfPBJ_--U";
const FR_CONTR_INFO = "1CZeTlX3dusvoAudywZwiFujBDie56v9VGZezLzylDPo"; 
const NL_CONTR_INFO = "1RLFx8PKmYqRxGjKqI5AUiEI131r0OjCGiPgNOtTsGyo";

function fetchKPIs() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const dataSheet = ss.getSheetByName("Data");

  const mergedMap = new Map();

  const slotClosingsMap = fetchSlotClosings();
  for (const [key, value] of slotClosingsMap.entries()) {
    mergedMap.set(key, value);
  }

  const hourBalancesMap = fetchHourBalances();
  for (const [key, value] of hourBalancesMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[5] = value[5];   // Cumulative balances
      existing[6] = value[6];   // Cumulative neg. balances
      existing[7] = value[7];   // Cumulative pos. balances
      existing[8] = value[8];   // Below neg. threshold
      existing[9] = value[9];   // Above pos. threshold
      existing[10] = value[10]; // People below neg. threshold
      existing[11] = value[11]; // People above pos. threshold
    }
  }

  const retentionMap = fetchRetention();
  for (const [key, value] of retentionMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[12] = value[12]; // R 12 week pool retention
      existing[13] = value[13]; // R 8 week pool retention
      existing[14] = value[14]; // R 8 week hire retention
      existing[15] = value[15]; // R+ 12 week pool retention
      existing[16] = value[16]; // R+ 8 week pool retention
      existing[17] = value[17]; // R+ 8 week hire retention
    }
  }

  const avgTenureMap = fetchAvgTenure(mergedMap);
  for (const [key, value] of avgTenureMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[18] = value[18]; // Avg. Tenure (d) in column S (index 18)
    }
  }

  const cancelsMap = fetchCancels();
  for (const [key, value] of cancelsMap.entries()) {
    if (!mergedMap.has(key)) {
      mergedMap.set(key, value);
    } else {
      const existing = mergedMap.get(key);
      existing[19] = value[19]; // Cancelled deliveries in column T (index 19)
      existing[20] = value[20]; // Dist cancelled deliveries in column U (index 20)
    }
  }

  const result = Array.from(mergedMap.values());
  result.sort((a, b) => parseInt(a[2], 10) - parseInt(b[2], 10));

  dataSheet.clearContents();
  const headers = [
    "Country", "Hub", "Week",
    "Sum trips", "Weighted slot closing hrs.",
    "Cumulative balances", "Cumulative neg. balances", "Cumulative pos. balances",
    "Hrs. below neg. threshold", "Hrs. above pos. threshold",
    "People below neg. threshold", "People above pos. threshold",
    "R 12 week pool retention", "R 8 week pool retention", "R 8 week hire retention",
    "R+ 12 week pool retention", "R+ 8 week pool retention", "R+ 8 week hire retention",
    "Avg. Tenure (d)", "Cancelled deliveries", "Dist cancelled deliveries"
  ];
  dataSheet.getRange(1, 1, 1, headers.length).setValues([headers]);
  if (result.length > 0) {
    dataSheet.getRange(2, 1, result.length, headers.length).setValues(result);
  }
}

function fetchSlotClosings() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(REPORTING_ID);
  const sourceSheet = reportingSheet.getSheetByName("iDWH");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const aggregationMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocation = row[4];

      if (rowCountry === country && rowLocation === location) {
        const week = row[7];
        const rawM = row[9];
        const rawAR = row[43];
        const sumDeliveries = rawM === "" ? null : parseFloat(rawM);
        const slotClosingHrs = rawAR === "" ? null : parseFloat(rawAR);

        if (sumDeliveries !== null && slotClosingHrs !== null) {
          const key = `${rowCountry}_${rowLocation}_${week}`;

          if (!aggregationMap.has(key)) {
            aggregationMap.set(key, { country: rowCountry, location: rowLocation, week, mSum: 0, weightedArSum: 0 });
          }

          const entry = aggregationMap.get(key);
          entry.mSum += sumDeliveries;
          entry.weightedArSum += sumDeliveries * slotClosingHrs;
        }
      }
    });
  });

  const resultMap = new Map();
  for (const [key, entry] of aggregationMap.entries()) {
    const weightedAr = entry.mSum > 0 ? entry.weightedArSum / entry.mSum : null;
    const row = [entry.country, entry.location, entry.week, entry.mSum, weightedAr, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null];
    resultMap.set(key, row);
  }
  return resultMap;
}

function fetchHourBalances() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iTIMEw");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    // Helper function to convert date to week number
    function getWeekNumber(date) {
      const d = new Date(date);
      d.setHours(0, 0, 0, 0);
      d.setDate(d.getDate() + 3 - (d.getDay() + 6) % 7);
      const week1 = new Date(d.getFullYear(), 0, 4);
      return 1 + Math.round(((d.getTime() - week1.getTime()) / 86400000 - 3 + (week1.getDay() + 6) % 7) / 7);
    }

    // Aggregate data by week for each metric
    const weeklyAggregates = new Map();

    sourceValues.forEach(row => {
      const rowCountry = row[8]; // Column I (0-indexed: 8)
      const rowLocation = row[9]; // Column J (0-indexed: 9)
      const dateValue = row[6];   // Column G (0-indexed: 6)
      const role = row[10];       // Column K (0-indexed: 10)

      if (rowCountry === country && rowLocation === location && role === "Runner") {
        const week = getWeekNumber(dateValue);
        const key = `${rowCountry}_${rowLocation}_${week}`;

        if (!weeklyAggregates.has(key)) {
          weeklyAggregates.set(key, {
            country: rowCountry,
            location: rowLocation,
            week: week,
            cumulativeBalance: 0,
            cumulativeNegBalance: 0,
            cumulativePosBalance: 0,
            belowNegThreshold: 0,
            abovePosThreshold: 0,
            peopleBelowNegThreshold: 0,
            peopleAbovePosThreshold: 0
          });
        }

        const aggregate = weeklyAggregates.get(key);
        const balanceValue = row[24] === "" ? 0 : parseFloat(row[24]); // Column Y (0-indexed: 24)
        const peopleCount = row[26] === "" ? 0 : parseFloat(row[26]);  // Column AA (0-indexed: 26)
        const isNegative = row[13]; // Column N (0-indexed: 13)
        const isBelowNegThreshold = row[14]; // Column O (0-indexed: 14)
        const isAbovePosThreshold = row[15]; // Column P (0-indexed: 15)

        // Cumulative balance (no filter)
        aggregate.cumulativeBalance += balanceValue;

        // Cumulative negative balance (N = FALSE)
        if (isNegative === false) {
          aggregate.cumulativeNegBalance += balanceValue;
        }

        // Cumulative positive balance (N = TRUE)
        if (isNegative === true) {
          aggregate.cumulativePosBalance += balanceValue;
        }

        // Below negative threshold (O = TRUE)
        if (isBelowNegThreshold === true) {
          aggregate.belowNegThreshold += balanceValue;
          aggregate.peopleBelowNegThreshold += peopleCount;
        }

        // Above positive threshold (P = TRUE)
        if (isAbovePosThreshold === true) {
          aggregate.abovePosThreshold += balanceValue;
          aggregate.peopleAbovePosThreshold += peopleCount;
        }
      }
    });

    // Convert aggregates to result format
    for (const [key, aggregate] of weeklyAggregates.entries()) {
      const rowData = [
        aggregate.country,
        aggregate.location,
        aggregate.week,
        null, // Sum deliveries (filled by fetchSlotClosings)
        null, // Weighted slot closing hrs (filled by fetchSlotClosings)
        aggregate.cumulativeBalance,
        aggregate.cumulativeNegBalance,
        aggregate.cumulativePosBalance,
        aggregate.belowNegThreshold,
        aggregate.abovePosThreshold,
        aggregate.peopleBelowNegThreshold,
        aggregate.peopleAbovePosThreshold,
        null, null, null, null, null, null, // Retention metrics (filled by fetchRetention)
        null, // Avg. Tenure (filled by fetchAvgTenure)
        null, null // Cancelled deliveries (filled by fetchCancels)
      ];

      resultMap.set(key, rowData);
    }
  });

  return resultMap;
}

function fetchRetention() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(OPS_REPORTING);
  const sourceSheet = reportingSheet.getSheetByName("iWD");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const resultMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocationRaw = row[9];
      const rowLocation = typeof rowLocationRaw === 'string' ? rowLocationRaw.slice(-3) : String(rowLocationRaw).slice(-3);

      if (rowCountry === country && rowLocation === location) {
        const week = row[6];
        const jobProfile = row[10];
        const emplType = row[11];

        if (emplType === "Total" && (jobProfile === "Runner" || jobProfile === "RunnerPlus")) {
          const ret12Pool = row[26] === "" ? null : parseFloat(row[26]);
          const ret8Pool = row[27] === "" ? null : parseFloat(row[27]);
          const ret8Hire = row[28] === "" ? null : parseFloat(row[28]);

          const key = `${rowCountry}_${location}_${week}`;

          if (!resultMap.has(key)) {
            resultMap.set(key, [
              rowCountry,
              location,
              week,
              null, null, // Sum deliveries, Weighted slot closing hrs
              null, null, null, null, null, null, null, // Hour Balances (7 columns)
              null, null, null, null, null, null, // Retention metrics (6 columns)
              null, // Avg. Tenure
              null, null // Cancelled deliveries
            ]);
          }

          const entry = resultMap.get(key);

          if (jobProfile === "Runner") {
            entry[12] = ret12Pool; // R 12 week pool retention
            entry[13] = ret8Pool;  // R 8 week pool retention
            entry[14] = ret8Hire;  // R 8 week hire retention
          } else if (jobProfile === "RunnerPlus") {
            entry[15] = ret12Pool; // R+ 12 week pool retention
            entry[16] = ret8Pool;  // R+ 8 week pool retention
            entry[17] = ret8Hire;  // R+ 8 week hire retention
          }
        }
      }
    });
  });

  return resultMap;
}

function fetchAvgTenure(existingWeeksMap) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const resultMap = new Map();
  const today = new Date();

  // Helper function to get contract sheet info based on country code from config
  function getContractSheetInfo(country) {
    switch(country) {
      case "DE":
        return {
          sheetId: DE_CONTR_INFO,
          sheetName: "DE - Distribution - Contract information"
        };
      case "FR":
        return {
          sheetId: FR_CONTR_INFO,
          sheetName: "FR - Distribution - Contract Information"
        };
      case "NL":
        return {
          sheetId: NL_CONTR_INFO,
          sheetName: "NL - Distribution - Contract Information"
        };
      default:
        return null;
    }
  }

  // Helper function to get start date of a week
  function getWeekStartDate(year, week) {
    const jan4 = new Date(year, 0, 4);
    const jan4Day = jan4.getDay() || 7; // Sunday = 7
    const weekStart = new Date(jan4);
    weekStart.setDate(jan4.getDate() - jan4Day + 1 + (week - 1) * 7);
    return weekStart;
  }

  // Extract all unique weeks from existing data
  const allWeeks = new Set();
  for (const key of existingWeeksMap.keys()) {
    const parts = key.split('_');
    if (parts.length >= 3) {
      allWeeks.add(parseInt(parts[2])); // week number
    }
  }

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    // Get contract sheet info for this country
    const contractInfo = getContractSheetInfo(country);
    if (!contractInfo) {
      console.warn(`No contract sheet configured for country: ${country}`);
      return;
    }

    let sourceValues;
    try {
      const contractSheet = SpreadsheetApp.openById(contractInfo.sheetId);
      const sourceSheet = contractSheet.getSheetByName(contractInfo.sheetName);
      sourceValues = sourceSheet.getDataRange().getValues();
    } catch (error) {
      console.error(`Error accessing contract sheet for ${country}:`, error);
      return;
    }

    // For each week found in existing data
    allWeeks.forEach(week => {
      const currentYear = new Date().getFullYear();
      const weekStartDate = getWeekStartDate(currentYear, week);
      const tenureDays = [];

      sourceValues.forEach(row => {
        const rowLocation = row[3]; // Column D (0-indexed: 3)
        const role = row[5]; // Column F (0-indexed: 5)

        // Filter by location and role "Runner"
        if (rowLocation === location && role === "Runner") {
          const startDate = row[12]; // Column M (0-indexed: 12)
          const endDate = row[16];   // Column Q (0-indexed: 16)

          // Only include entries where start date is before the week start date
          if (startDate && startDate instanceof Date && startDate <= weekStartDate) {
            // Use end date if available, otherwise use today
            const effectiveEndDate = (endDate && endDate instanceof Date) ? endDate : today;

            // Calculate difference in days
            const diffTime = effectiveEndDate.getTime() - startDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            // Only include positive differences (ignore negative values)
            if (diffDays >= 0) {
              tenureDays.push(diffDays);
            }
          }
        }
      });

      // Calculate average tenure for this week
      let avgTenure = null;
      if (tenureDays.length > 0) {
        const sum = tenureDays.reduce((acc, days) => acc + days, 0);
        avgTenure = sum / tenureDays.length;
      }

      // Create week-specific key and result
      const key = `${country}_${location}_${week}`;
      const rowData = [
        country,
        location,
        week,
        null, null, // Sum deliveries, Weighted slot closing hrs (filled by fetchSlotClosings)
        null, null, null, null, null, null, null, // Hour Balances (7 columns, filled by fetchHourBalances)
        null, null, null, null, null, null, // Retention metrics (6 columns, filled by fetchRetention)
        avgTenure, // Avg. Tenure (d)
        null, null // Cancelled deliveries (filled by fetchCancels)
      ];

      resultMap.set(key, rowData);
    });
  });

  return resultMap;
}

function fetchCancels() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");
  const reportingSheet = SpreadsheetApp.openById(REPORTING_ID);
  const sourceSheet = reportingSheet.getSheetByName("iDWH");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const sourceValues = sourceSheet.getDataRange().getValues();

  const aggregationMap = new Map();

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    sourceValues.forEach(row => {
      const rowCountry = row[3];
      const rowLocation = row[4];

      if (rowCountry === country && rowLocation === location) {
        const week = row[7];
        const rawAE = row[30]; // Column AE (0-indexed: 30)
        const rawDY = row[128]; // Column DY (0-indexed: 128)
        const cancelledDeliveries = rawAE === "" ? 0 : parseFloat(rawAE);
        const distCancelledDeliveries = rawDY === "" ? 0 : parseFloat(rawDY);

        const key = `${rowCountry}_${rowLocation}_${week}`;

        if (!aggregationMap.has(key)) {
          aggregationMap.set(key, {
            country: rowCountry,
            location: rowLocation,
            week,
            cancelledSum: 0,
            distCancelledSum: 0
          });
        }

        const entry = aggregationMap.get(key);
        entry.cancelledSum += cancelledDeliveries;
        entry.distCancelledSum += distCancelledDeliveries;
      }
    });
  });

  const resultMap = new Map();
  for (const [key, entry] of aggregationMap.entries()) {
    const row = [
      entry.country,
      entry.location,
      entry.week,
      null, null, // Sum deliveries, Weighted slot closing hrs (filled by fetchSlotClosings)
      null, null, null, null, null, null, null, // Hour Balances (7 columns, filled by fetchHourBalances)
      null, null, null, null, null, null, // Retention metrics (6 columns, filled by fetchRetention)
      null, // Avg. Tenure (filled by fetchAvgTenure)
      entry.cancelledSum, // Cancelled deliveries
      entry.distCancelledSum // Dist cancelled deliveries
    ];
    resultMap.set(key, row);
  }
  return resultMap;
}
