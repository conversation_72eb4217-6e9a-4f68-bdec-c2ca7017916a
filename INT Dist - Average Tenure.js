const REPORTING_ID = "1trjbOiLgOzrzv57AAomOfXjFQOmWsRS3cT_tvdRzdhg";
const OPS_REPORTING = "1MHbY6Oz-SiFloV5re7FSHDNqDf4WTMbKtGN1oqbrPcg"; 
const DE_CONTR_INFO = "1V7LCdx7YrNr0_GRyfgSZTEoDsBtgcnF3i3RfPBJ_--U";
const FR_CONTR_INFO = "1CZeTlX3dusvoAudywZwiFujBDie56v9VGZezLzylDPo"; 
const NL_CONTR_INFO = "1RLFx8PKmYqRxGjKqI5AUiEI131r0OjCGiPgNOtTsGyo";

function fetchAvgTenure(existingWeeksMap) {
  const ss = SpreadsheetApp.getActiveSpreadsheet();
  const configSheet = ss.getSheetByName("Config");

  const configData = configSheet.getRange(2, 1, configSheet.getLastRow() - 1, 2).getValues();
  const resultMap = new Map();
  const today = new Date();

  // Helper function to get contract sheet info based on country code from config
  function getContractSheetInfo(country) {
    switch(country) {
      case "DE":
        return {
          sheetId: DE_CONTR_INFO,
          sheetName: "DE - Distribution - Contract information"
        };
      case "FR":
        return {
          sheetId: FR_CONTR_INFO,
          sheetName: "FR - Distribution - Contract Information"
        };
      case "NL":
        return {
          sheetId: NL_CONTR_INFO,
          sheetName: "NL - Distribution - Contract Information"
        };
      default:
        return null;
    }
  }

  // Helper function to get start date of a week
  function getWeekStartDate(year, week) {
    const jan4 = new Date(year, 0, 4);
    const jan4Day = jan4.getDay() || 7; // Sunday = 7
    const weekStart = new Date(jan4);
    weekStart.setDate(jan4.getDate() - jan4Day + 1 + (week - 1) * 7);
    return weekStart;
  }

  // Extract all unique weeks from existing data
  const allWeeks = new Set();
  for (const key of existingWeeksMap.keys()) {
    const parts = key.split('_');
    if (parts.length >= 3) {
      allWeeks.add(parseInt(parts[2])); // week number
    }
  }

  configData.forEach(([country, location]) => {
    if (!country || !location) return;

    // Get contract sheet info for this country
    const contractInfo = getContractSheetInfo(country);
    if (!contractInfo) {
      console.warn(`No contract sheet configured for country: ${country}`);
      return;
    }

    let sourceValues;
    try {
      const contractSheet = SpreadsheetApp.openById(contractInfo.sheetId);
      const sourceSheet = contractSheet.getSheetByName(contractInfo.sheetName);
      sourceValues = sourceSheet.getDataRange().getValues();
    } catch (error) {
      console.error(`Error accessing contract sheet for ${country}:`, error);
      return;
    }

    // For each week found in existing data
    allWeeks.forEach(week => {
      const currentYear = new Date().getFullYear();
      const weekStartDate = getWeekStartDate(currentYear, week);
      const tenureDays = [];

      sourceValues.forEach(row => {
        const rowLocation = row[3]; // Column D (0-indexed: 3)
        const role = row[5]; // Column F (0-indexed: 5)

        // Filter by location and role "Runner"
        if (rowLocation === location && role === "Runner") {
          const startDate = row[12]; // Column M (0-indexed: 12)
          const endDate = row[16];   // Column Q (0-indexed: 16)

          // Only include entries where start date is before the week start date
          if (startDate && startDate instanceof Date && startDate <= weekStartDate) {
            // Use end date if available, otherwise use today
            const effectiveEndDate = (endDate && endDate instanceof Date) ? endDate : today;

            // Calculate difference in days
            const diffTime = effectiveEndDate.getTime() - startDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            // Only include positive differences (ignore negative values)
            if (diffDays >= 0) {
              tenureDays.push(diffDays);
            }
          }
        }
      });

      // Calculate average tenure for this week
      let avgTenure = null;
      if (tenureDays.length > 0) {
        const sum = tenureDays.reduce((acc, days) => acc + days, 0);
        avgTenure = sum / tenureDays.length;
      }

      // Create week-specific key and result
      const key = `${country}_${location}_${week}`;
      const rowData = [
        country,
        location,
        week,
        null, null, // Sum deliveries, Weighted slot closing hrs (filled by fetchSlotClosings)
        null, null, null, null, null, null, null, // Hour Balances (7 columns, filled by fetchHourBalances)
        null, null, null, null, null, null, // Retention metrics (6 columns, filled by fetchRetention)
        avgTenure, // Avg. Tenure (d)
        null, null // Cancelled deliveries (filled by fetchCancels)
      ];

      resultMap.set(key, rowData);
    });
  });

  return resultMap;
}