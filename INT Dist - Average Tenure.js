const DE_CONTR_INFO = "1V7LCdx7YrNr0_GRyfgSZTEoDsBtgcnF3i3RfPBJ_--U";
const FR_CONTR_INFO = "1CZeTlX3dusvoAudywZwiFujBDie56v9VGZezLzylDPo";
const NL_CONTR_INFO = "1RLFx8PKmYqRxGjKqI5AUiEI131r0OjCGiPgNOtTsGyo";

function updateAvgTenureForCurrentWeek() {
  const ss = SpreadsheetApp.getActiveSpreadsheet();

  // Get or create the Avg_tenure sheet
  let dataSheet = ss.getSheetByName("Avg_tenure");
  if (!dataSheet) {
    dataSheet = ss.insertSheet("Avg_tenure");
    console.log("Created new sheet: Avg_tenure");
  }

  const today = new Date();
  const currentWeek = getCurrentWeekNumber();
  const currentYear = new Date().getFullYear();

  // Get all countries and their contract sheet info
  const countries = [
    { code: "DE", sheetId: DE_CONTR_INFO, sheetName: "DE - Distribution - Contract information" },
    { code: "FR", sheetId: FR_CONTR_INFO, sheetName: "FR - Distribution - Contract Information" },
    { code: "NL", sheetId: NL_CONTR_INFO, sheetName: "NL - Distribution - Contract Information" }
  ];

  // Helper function to get current week number (ISO week)
  function getCurrentWeekNumber() {
    const date = new Date();
    const jan4 = new Date(date.getFullYear(), 0, 4);
    const jan4Day = jan4.getDay() || 7;
    const weekStart = new Date(jan4);
    weekStart.setDate(jan4.getDate() - jan4Day + 1);

    const diffTime = date.getTime() - weekStart.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return Math.floor(diffDays / 7) + 1;
  }

  // Helper function to extract unique locations from contract sheet
  function getLocationsFromSheet(sheetId, sheetName) {
    try {
      const contractSheet = SpreadsheetApp.openById(sheetId);
      const sourceSheet = contractSheet.getSheetByName(sheetName);
      const sourceHubs = sourceSheet.getDataRange().getValues();

      const locations = new Set();
      sourceHubs.forEach((row, index) => {
        if (index === 0) return; // Skip header row
        const location = row[3]; // Column D (0-indexed: 3)
        if (location && typeof location === 'string' && location.trim() !== '' && location.trim().length === 3) {
          locations.add(location.trim());
        }
      });

      return Array.from(locations);
    } catch (error) {
      console.error(`Error accessing sheet ${sheetName}:`, error);
      return [];
    }
  }

  // Helper function to get start date of current week
  function getCurrentWeekStartDate() {
    const jan4 = new Date(currentYear, 0, 4);
    const jan4Day = jan4.getDay() || 7;
    const weekStart = new Date(jan4);
    weekStart.setDate(jan4.getDate() - jan4Day + 1 + (currentWeek - 1) * 7);
    return weekStart;
  }

  // Find existing rows for current week and return their positions
  function findExistingCurrentWeekRows() {
    const lastRow = dataSheet.getLastRow();
    if (lastRow <= 1) {
      return []; // Sheet is empty or only has header row
    }

    try {
      const existingData = dataSheet.getDataRange().getValues();
      const existingRows = [];

      for (let i = 1; i < existingData.length; i++) { // Skip header row
        const row = existingData[i];
        if (row[2] === currentWeek && row[3] === currentYear) { // Check week and year columns
          existingRows.push({
            rowIndex: i + 1, // +1 because sheet rows are 1-indexed
            country: row[0],
            location: row[1]
          });
        }
      }

      return existingRows;
    } catch (error) {
      console.warn('Error checking existing data, continuing with fresh data:', error);
      return [];
    }
  }

  // Ensure sheet has proper headers
  function ensureSheetHeaders() {
    const lastRow = dataSheet.getLastRow();

    if (lastRow === 0) {
      // Sheet is completely empty, add headers
      const headers = ['Country', 'Location', 'Week', 'Year', 'Avg_Tenure_Days', 'Last_Updated'];
      dataSheet.getRange(1, 1, 1, headers.length).setValues([headers]);

      // Format headers
      const headerRange = dataSheet.getRange(1, 1, 1, headers.length);
      headerRange.setFontWeight('bold');
      headerRange.setBackground('#f0f0f0');
    }
  }

  // Ensure sheet has proper headers if empty
  ensureSheetHeaders();

  // Find existing rows for current week
  const existingRows = findExistingCurrentWeekRows();
  const existingRowsMap = new Map();
  existingRows.forEach(row => {
    const key = `${row.country}_${row.location}`;
    existingRowsMap.set(key, row.rowIndex);
  });

  const weekStartDate = getCurrentWeekStartDate();
  const newRows = [];
  const updateRows = [];

  // Process each country
  countries.forEach(country => {
    // Get all unique locations from this country's sheet
    const locations = getLocationsFromSheet(country.sheetId, country.sheetName);

    // Get source data once for this country
    let sourceHubs;
    try {
      const contractSheet = SpreadsheetApp.openById(country.sheetId);
      const sourceSheet = contractSheet.getSheetByName(country.sheetName);
      sourceHubs = sourceSheet.getDataRange().getValues();
    } catch (error) {
      console.error(`Error accessing contract sheet for ${country.code}:`, error);
      return;
    }

    // Process each location found in this country
    locations.forEach(location => {
      const tenureDays = [];

      sourceHubs.forEach(row => {
        const rowLocation = row[3]; // Column D (0-indexed: 3)
        const status = row[4]; // Column E (0-indexed: 4)
        const role = row[5]; // Column F (0-indexed: 5)

        // Filter by location, role "Runner", and exclude "Left" status
        if (rowLocation === location && role === "Runner" && status !== "Left") {
          const startDate = row[12]; // Column M (0-indexed: 12)
          const endDate = row[16];   // Column Q (0-indexed: 16)

          // Only include entries where start date is before the week start date
          if (startDate && startDate instanceof Date && startDate <= weekStartDate) {
            // Use end date if available, otherwise use today
            const effectiveEndDate = (endDate && endDate instanceof Date) ? endDate : today;

            // Calculate difference in days
            const diffTime = effectiveEndDate.getTime() - startDate.getTime();
            const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

            // Only include positive differences (ignore negative values)
            if (diffDays >= 0) {
              tenureDays.push(diffDays);
            }
          }
        }
      });

      // Calculate average tenure for current week
      let avgTenure = null;
      if (tenureDays.length > 0) {
        const sum = tenureDays.reduce((acc, days) => acc + days, 0);
        avgTenure = Math.round((sum / tenureDays.length) * 100) / 100; // Round to 2 decimal places
      }

      // Create row data for current week
      const rowData = [
        country.code,
        location,
        currentWeek,
        currentYear,
        avgTenure,
        new Date() // Timestamp when data was updated
      ];

      // Check if this country/location combination already exists
      const key = `${country.code}_${location}`;
      if (existingRowsMap.has(key)) {
        // Update existing row
        updateRows.push({
          rowIndex: existingRowsMap.get(key),
          data: rowData
        });
      } else {
        // Add as new row
        newRows.push(rowData);
      }
    });
  });

  // Update existing rows and add new rows
  try {
    // Update existing rows
    updateRows.forEach(update => {
      dataSheet.getRange(update.rowIndex, 1, 1, update.data.length).setValues([update.data]);
    });

    // Add new rows
    if (newRows.length > 0) {
      const lastRow = dataSheet.getLastRow();
      dataSheet.getRange(lastRow + 1, 1, newRows.length, newRows[0].length).setValues(newRows);
    }

    const totalUpdated = updateRows.length + newRows.length;
    console.log(`Updated average tenure data for week ${currentWeek}/${currentYear}: ${updateRows.length} updated, ${newRows.length} new entries (total: ${totalUpdated})`);

  } catch (error) {
    console.error('Error writing data to sheet:', error);
    throw error;
  }

  if (updateRows.length === 0 && newRows.length === 0) {
    console.log(`No data found for week ${currentWeek}/${currentYear}`);
  }
}